# 流量统计功能说明

## 概述

本项目实现了完整的流量统计功能，包括服务器端和客户端的流量监控。流量统计可以实时跟踪数据传输量，支持上传、下载和总流量的统计。

## 服务器端流量统计

### 架构设计

服务器端采用事件驱动的流量统计架构：

1. **TrafficManager** - 流量管理器，负责处理流量事件
2. **TrafficHandler** - 流量处理器接口，支持多种统计处理器
3. **ServerTrafficHandler** - 服务器级流量统计
4. **UserTrafficHandler** - 用户级流量统计

### 主要组件

#### TrafficManager
```go
// 创建流量管理器
tm := NewTrafficManager(1000) // 1000个事件缓冲区

// 注册处理器
tm.RegisterHandler(NewServerTrafficHandler(server))
tm.RegisterHandler(NewUserTrafficHandler(connectionManager, authManager))

// 更新流量统计
tm.UpdateTraffic(streamID, connectionID, userID, targetAddr, uploaded, downloaded)
```

#### SOCKSConnection
```go
// 创建SOCKS连接
socksConn := NewSOCKSConnection(stream)

// 启动数据转发（带流量统计）
socksConn.StartForwarding(
    func(streamID quic.StreamID, uploaded, downloaded int64) {
        // 实时流量回调
        tm.UpdateTraffic(streamID, connectionID, userID, targetAddr, uploaded, downloaded)
    },
    func(streamID quic.StreamID, targetAddr string, uploaded, downloaded int64, duration time.Duration) {
        // 连接关闭回调
        eventLogger.TCPClose(clientAddr, targetAddr, streamID, uploaded, downloaded, duration)
    },
)
```

### 统计信息

服务器端提供以下统计信息：

- **服务器级统计**：
  - 总上传字节数
  - 总下载字节数
  - 运行时间
  - QUIC连接数
  - Stream数量

- **用户级统计**：
  - 每个用户的流量使用情况
  - 连接时长
  - Stream数量

### 使用示例

```go
// 获取服务器统计
stats := server.GetStats()
fmt.Printf("总上传: %s\n", formatBytes(stats["traffic"].(map[string]int64)["uploaded"]))
fmt.Printf("总下载: %s\n", formatBytes(stats["traffic"].(map[string]int64)["downloaded"]))

// 获取详细流量统计
trafficStats := server.GetTrafficStats()
fmt.Printf("详细统计: %+v\n", trafficStats)
```

## 客户端流量统计

### SDK 功能

客户端 SDK 提供以下流量统计功能：

#### 核心函数

```c
// 获取上传字节数
int64_t GetUploadBytes();

// 获取下载字节数
int64_t GetDownloadBytes();

// 获取总字节数
int64_t GetTotalBytes();

// 重置流量统计
int ResetTrafficStats();

// 获取连接统计信息
char* GetConnectionStats();

// 获取格式化的统计信息
char* GetFormattedTrafficStats();
```

#### 使用示例

```c
#include "sdk.h"

int main() {
    // 连接服务器
    Connect("localhost:8080", "token", "client-id", 0);
    
    // 建立IO桥接
    IOBridge("example.com:80", socket_fd);
    
    // 获取流量统计
    int64_t upload = GetUploadBytes();
    int64_t download = GetDownloadBytes();
    int64_t total = GetTotalBytes();
    
    printf("上传: %lld 字节\n", upload);
    printf("下载: %lld 字节\n", download);
    printf("总计: %lld 字节\n", total);
    
    // 获取格式化的统计信息
    char* stats = GetFormattedTrafficStats();
    printf("统计: %s\n", stats);
    
    // 重置统计
    ResetTrafficStats();
    
    return 0;
}
```

### 实现原理

客户端流量统计通过自定义的 `copyWithStats` 函数实现：

```go
func copyWithStats(dst io.Writer, src io.Reader, isUpload bool) (written int64, err error) {
    buffer := make([]byte, 32*1024) // 32KB 缓冲区
    
    for {
        nr, er := src.Read(buffer)
        if nr > 0 {
            nw, ew := dst.Write(buffer[0:nr])
            if nw > 0 {
                written += int64(nw)
                // 实时更新流量统计
                if isUpload {
                    atomic.AddInt64(&UploadBytes, int64(nw))
                } else {
                    atomic.AddInt64(&DownloadBytes, int64(nw))
                }
            }
            // ... 错误处理
        }
        // ... 结束处理
    }
    return written, err
}
```

## 流量格式化

项目提供统一的流量格式化函数：

```go
func formatBytes(bytes int64) string {
    const unit = 1024
    if bytes < unit {
        return fmt.Sprintf("%d B", bytes)
    }
    div, exp := int64(unit), 0
    for n := bytes / unit; n >= unit; n /= unit {
        div *= unit
        exp++
    }
    return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
```

支持的单位：B, KB, MB, GB, TB, PB, EB

## 性能优化

### 服务器端优化

1. **事件驱动架构**：使用异步事件处理，避免阻塞主线程
2. **缓冲区机制**：使用固定大小的缓冲区处理流量事件
3. **原子操作**：使用 `sync/atomic` 包确保线程安全
4. **并发处理**：支持多个流量处理器并发工作

### 客户端优化

1. **原子操作**：使用 `atomic.AddInt64` 确保线程安全
2. **高效缓冲区**：使用 32KB 缓冲区优化数据传输
3. **实时统计**：在数据传输过程中实时更新统计

## 监控和日志

### 服务器端监控

```go
// 定期输出统计信息
func (s *Server) printStats() {
    uptime := time.Since(s.startTime)
    uploaded := atomic.LoadInt64(&s.totalBytesUploaded)
    downloaded := atomic.LoadInt64(&s.totalBytesDownloaded)
    
    log.Printf("📊 === 服务器统计信息 ===")
    log.Printf("⏱️  运行时间: %v", uptime)
    log.Printf("📈 数据传输 - 上传: %s, 下载: %s", 
        formatBytes(uploaded), formatBytes(downloaded))
}
```

### 事件日志

```go
// TCP连接关闭事件
func (l *DefaultEventLogger) TCPClose(clientAddr net.Addr, targetAddr string, 
    streamID uint64, uploaded, downloaded int64, duration time.Duration) {
    log.Printf("🔚 [Stream %d] TCP连接关闭: %s -> %s, 上传: %s, 下载: %s, 持续: %v",
        streamID, clientAddr, targetAddr,
        formatBytes(uploaded), formatBytes(downloaded), duration)
}
```

## 测试

项目包含完整的流量统计测试：

```bash
# 运行流量统计测试
go test ./test -v -run TestTrafficStats

# 运行流量格式化测试
go test ./test -v -run TestFormatBytes

# 运行并发流量统计测试
go test ./test -v -run TestConcurrentTrafficStats
```

## 注意事项

1. **内存使用**：流量统计使用原子操作，内存开销很小
2. **线程安全**：所有统计操作都是线程安全的
3. **性能影响**：流量统计对数据传输性能影响微乎其微
4. **数据准确性**：统计数据在连接异常关闭时可能不完整
5. **重置操作**：重置统计会清空所有历史数据

## 扩展功能

### 自定义流量处理器

```go
type CustomTrafficHandler struct {
    // 自定义字段
}

func (h *CustomTrafficHandler) HandleTraffic(event *TrafficEvent) {
    // 自定义处理逻辑
}

func (h *CustomTrafficHandler) GetStats() map[string]interface{} {
    // 返回自定义统计
    return map[string]interface{}{
        "custom_metric": "value",
    }
}

func (h *CustomTrafficHandler) GetName() string {
    return "custom"
}

func (h *CustomTrafficHandler) Close() {
    // 清理资源
}

// 注册自定义处理器
trafficManager.RegisterHandler(&CustomTrafficHandler{})
```

### 流量限制

可以基于流量统计实现流量限制功能：

```go
// 检查用户流量限制
func (am *AuthManager) CheckTrafficLimit(userID string, bytes int64) bool {
    // 实现流量限制逻辑
    return true
}
```

## 总结

流量统计功能为项目提供了完整的流量监控能力，支持：

- 实时流量统计
- 多级统计（服务器级、用户级）
- 线程安全的并发处理
- 高效的性能表现
- 易于扩展的架构设计

这些功能使得项目能够有效监控和管理网络流量使用情况。 