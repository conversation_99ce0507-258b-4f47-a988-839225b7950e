package server

import (
	"fmt"
	"net"
	"sync"
	"sync/atomic"

	"github.com/quic-go/quic-go"
)

// QUICConnectionHandler QUIC连接处理器
// 实现ConnectionHandler接口，用于统一连接管理
type QUICConnectionHandler struct {
	*BaseConnection

	// QUIC连接
	conn quic.Connection

	// 服务器引用
	server *Server

	// 连接状态
	mu            sync.RWMutex
	closed        bool
	authenticated bool

	// 流管理
	activeStreams int64
	totalStreams  int64

	// 认证信息
	userID   string
	clientID string

	// 清理函数
	cleanupOnce sync.Once
}

// NewQUICConnectionHandler 创建新的QUIC连接处理器
func NewQUICConnectionHandler(server *Server, conn quic.Connection) *QUICConnectionHandler {
	clientAddr := conn.RemoteAddr()
	localAddr := conn.LocalAddr()

	// 创建基础连接
	base := NewBaseConnection(clientAddr, localAddr, ConnectionTypeQUIC)

	// 创建QUIC连接
	quicConn := &QUICConnectionHandler{
		BaseConnection: base,
		conn:           conn,
		server:         server,
		closed:         false,
		authenticated:  false,
	}

	return quicConn
}

// Start 启动QUIC连接处理
func (c *QUICConnectionHandler) Start() error {
	c.SetStatus("starting")

	// 注册到连接管理器
	if err := c.server.connectionManager.AddConnection(c); err != nil {
		c.SetStatus("failed")
		// 如果是达到连接限制，记录统计
		if err.Error() == "max connections reached" {
			atomic.AddInt64(&c.server.rejectedConnections, 1)
		}
		return fmt.Errorf("注册连接失败: %w", err)
	}

	c.SetStatus("active")
	return nil
}

// Stop 停止QUIC连接处理
func (c *QUICConnectionHandler) Stop() error {
	c.SetStatus("stopping")
	return c.Close()
}

// Close 关闭QUIC连接
func (c *QUICConnectionHandler) Close() error {
	c.cleanupOnce.Do(func() {
		c.mu.Lock()
		c.closed = true
		c.mu.Unlock()

		c.SetStatus("closed")

		// 从连接管理器移除
		c.server.connectionManager.RemoveConnection(c.GetConnID())

		// 关闭基础连接
		c.BaseConnection.Close()

		// 关闭QUIC连接
		if c.conn != nil {
			c.conn.CloseWithError(0, "connection closed")
		}
	})

	return nil
}

// IsClosed 检查连接是否已关闭
func (c *QUICConnectionHandler) IsClosed() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.closed
}

// SetAuthenticated 设置认证状态
func (c *QUICConnectionHandler) SetAuthenticated(userID, clientID string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.authenticated = true
	c.userID = userID
	c.clientID = clientID
	c.SetAuthInfo(userID, clientID)
	c.SetStatus("authenticated")
}

// IsAuthenticated 检查是否已认证
func (c *QUICConnectionHandler) IsAuthenticated() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.authenticated
}

// AddStream 增加流计数
func (c *QUICConnectionHandler) AddStream() {
	atomic.AddInt64(&c.totalStreams, 1)
	atomic.AddInt64(&c.activeStreams, 1)
	c.BaseConnection.AddStream()
}

// RemoveStream 减少流计数
func (c *QUICConnectionHandler) RemoveStream() {
	if atomic.LoadInt64(&c.activeStreams) > 0 {
		atomic.AddInt64(&c.activeStreams, -1)
	}
	c.BaseConnection.RemoveStream()
}

// GetActiveStreams 获取活跃流数量
func (c *QUICConnectionHandler) GetActiveStreams() int64 {
	return atomic.LoadInt64(&c.activeStreams)
}

// GetTotalStreams 获取总流数量
func (c *QUICConnectionHandler) GetTotalStreams() int64 {
	return atomic.LoadInt64(&c.totalStreams)
}

// GetQUICConnection 获取底层QUIC连接
func (c *QUICConnectionHandler) GetQUICConnection() quic.Connection {
	return c.conn
}

// GetStats 获取QUIC连接统计（重写以添加协议特定信息）
func (c *QUICConnectionHandler) GetStats() ConnectionStats {
	stats := c.BaseConnection.GetStats()

	// 添加QUIC特定统计
	if stats.Extra == nil {
		stats.Extra = make(map[string]interface{})
	}
	stats.Extra["authenticated"] = c.IsAuthenticated()
	stats.Extra["user_id"] = c.userID
	stats.Extra["client_id"] = c.clientID
	stats.Extra["active_streams"] = atomic.LoadInt64(&c.activeStreams)
	stats.Extra["total_streams"] = atomic.LoadInt64(&c.totalStreams)

	// 添加QUIC连接状态
	if c.conn != nil {
		connState := c.conn.ConnectionState()
		stats.Extra["tls_version"] = connState.TLS.Version
		stats.Extra["did_resume"] = connState.TLS.DidResume
		stats.Extra["used_0rtt"] = connState.Used0RTT
	}

	return stats
}

// GetConnectionInfo 获取QUIC连接信息（重写以添加协议特定信息）
func (c *QUICConnectionHandler) GetConnectionInfo() ConnectionInfo {
	info := c.BaseConnection.GetConnectionInfo()

	// 添加QUIC特定信息
	info.UserID = c.userID
	info.ClientID = c.clientID
	info.IdleTimeout = c.server.config.IdleTimeout

	// 添加TLS信息（如果可用）
	if c.conn != nil {
		connState := c.conn.ConnectionState()
		switch connState.TLS.Version {
		case 0x0304:
			info.TLSVersion = "TLS 1.3"
		case 0x0303:
			info.TLSVersion = "TLS 1.2"
		default:
			info.TLSVersion = fmt.Sprintf("TLS 0x%04x", connState.TLS.Version)
		}
	}

	return info
}

func (c *QUICConnectionHandler) GetClientAddr() net.Addr {
	return c.conn.RemoteAddr()
}

// SetAuthToken 设置认证token（实现ConnectionHandler接口）
func (c *QUICConnectionHandler) SetAuthToken(token string) {
	c.BaseConnection.SetAuthToken(token)
}

// GetAuthStateFromGlobal 获取AuthState（实现ConnectionHandler接口）
func (c *QUICConnectionHandler) GetAuthStateFromGlobal() *AuthState {
	return c.BaseConnection.GetAuthStateFromGlobal()
}
