package server

import (
	"sync/atomic"
	"time"
)

// ServerTrafficHandler 服务器级流量统计处理器（直接引用Server统计字段）
type ServerTrafficHandler struct {
	server *Server // 直接引用Server实例
}

// NewServerTrafficHandler 创建服务器级流量统计处理器
func NewServerTrafficHandler(server *Server) *ServerTrafficHandler {
	return &ServerTrafficHandler{
		server: server,
	}
}

func (h *ServerTrafficHandler) HandleTraffic(event *TrafficEvent) {
	// 直接更新Server的统计字段
	atomic.AddInt64(&h.server.totalBytesUploaded, event.Uploaded)
	atomic.AddInt64(&h.server.totalBytesDownloaded, event.Downloaded)
}

func (h *ServerTrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_uploaded":   atomic.LoadInt64(&h.server.totalBytesUploaded),
		"total_downloaded": atomic.LoadInt64(&h.server.totalBytesDownloaded),
		"uptime_seconds":   time.Since(h.server.startTime).Seconds(),
		"start_time":       h.server.startTime.Unix(),
	}
}

func (h *ServerTrafficHandler) GetName() string {
	return "server_quic"
}

func (h *ServerTrafficHandler) Close() {
	// 服务器级处理器无需特殊清理
}

// HTTP2TrafficHandler HTTP2服务器级流量统计处理器
type HTTP2TrafficHandler struct {
	server *HTTP2Server // 直接引用HTTP2Server实例
}

// NewHTTP2TrafficHandler 创建HTTP2服务器级流量统计处理器
func NewHTTP2TrafficHandler(server *HTTP2Server) *HTTP2TrafficHandler {
	return &HTTP2TrafficHandler{
		server: server,
	}
}

func (h *HTTP2TrafficHandler) HandleTraffic(event *TrafficEvent) {
	// 直接更新HTTP2Server的统计字段
	atomic.AddInt64(&h.server.totalBytesUploaded, event.Uploaded)
	atomic.AddInt64(&h.server.totalBytesDownloaded, event.Downloaded)
}

func (h *HTTP2TrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_uploaded":   atomic.LoadInt64(&h.server.totalBytesUploaded),
		"total_downloaded": atomic.LoadInt64(&h.server.totalBytesDownloaded),
		"uptime_seconds":   time.Since(h.server.startTime).Seconds(),
		"start_time":       h.server.startTime.Unix(),
	}
}

func (h *HTTP2TrafficHandler) GetName() string {
	return "server_http2"
}

func (h *HTTP2TrafficHandler) Close() {
	// HTTP2服务器级处理器无需特殊清理
}

// UserTrafficHandler 用户级流量统计处理器（通过ConnectionManager获取认证状态）
type UserTrafficHandler struct {
	connectionManager *ConnectionManager // 连接管理器
}

// NewUserTrafficHandler 创建用户级流量统计处理器
func NewUserTrafficHandler(connectionManager *ConnectionManager) *UserTrafficHandler {
	return &UserTrafficHandler{
		connectionManager: connectionManager,
	}
}

func (h *UserTrafficHandler) HandleTraffic(event *TrafficEvent) {
	// 通过ConnectionManager查找连接，然后通过token获取AuthState
	if event.ConnectionID != "" && event.TrafficType == TrafficTypeQUIC {
		if conn, exists := h.connectionManager.GetConnection(event.ConnectionID); exists {
			if authState := conn.GetAuthStateFromGlobal(); authState != nil {
				// 直接更新AuthState统计
				atomic.AddInt64(&authState.BytesUp, event.Uploaded)
				atomic.AddInt64(&authState.BytesDown, event.Downloaded)
				authState.LastActivity = time.Now()
			}
		}
	}

	// HTTP2流量统计通过token查找AuthState（保持原有逻辑）
	if event.AuthToken != "" && event.TrafficType == TrafficTypeHTTP2 {
		// 这里需要通过某种方式获取AuthManager来查找token对应的AuthState
		// 暂时保留原有逻辑，后续可以进一步优化
		if server := GetGlobalServer(); server != nil {
			if authManager := server.GetAuthManager(); authManager != nil {
				if value, exists := authManager.authCache.Load(event.AuthToken); exists {
					authState := value.(*AuthState)
					atomic.AddInt64(&authState.BytesUp, event.Uploaded)
					atomic.AddInt64(&authState.BytesDown, event.Downloaded)
					authState.LastActivity = time.Now()
				}
			}
		}
	}
}

func (h *UserTrafficHandler) GetStats() map[string]interface{} {
	connections := make([]map[string]interface{}, 0)
	totalConnections := 0

	// 遍历所有连接，获取认证状态
	allConnections := h.connectionManager.GetAllConnections()
	for connID, conn := range allConnections {
		if authState := conn.GetAuthStateFromGlobal(); authState != nil {
			connStats := map[string]interface{}{
				"connection_id": connID,
				"user_id":       authState.UserID,
				"client_id":     authState.ClientID,
				"auth_time":     authState.AuthTime.Unix(),
				"expires_at":    authState.ExpiresAt.Unix(),
				"last_activity": authState.LastActivity.Unix(),
				"stream_count":  authState.StreamCount,
				"bytes_up":      authState.BytesUp,
				"bytes_down":    authState.BytesDown,
			}
			connections = append(connections, connStats)
			totalConnections++
		}
	}

	return map[string]interface{}{
		"total_users": totalConnections,
		"users":       connections,
	}
}

func (h *UserTrafficHandler) GetName() string {
	return "user"
}

func (h *UserTrafficHandler) Close() {
	// 用户级处理器无需特殊清理，AuthManager 自己管理生命周期
}

type TotalTrafficHandler struct {
	server *MultiProtocolServer
}

func NewTotalTrafficHandler(server *MultiProtocolServer) *TotalTrafficHandler {
	return &TotalTrafficHandler{
		server: server,
	}
}

func (h *TotalTrafficHandler) HandleTraffic(event *TrafficEvent) {
	atomic.AddInt64(&h.server.bytesUp, event.Uploaded)
	atomic.AddInt64(&h.server.bytesDown, event.Downloaded)
}

func (h *TotalTrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_uploaded":   atomic.LoadInt64(&h.server.bytesUp),
		"total_downloaded": atomic.LoadInt64(&h.server.bytesDown),
	}
}

func (h *TotalTrafficHandler) GetName() string {
	return "total"
}

func (h *TotalTrafficHandler) Close() {
	// 总流量处理器无需特殊清理
}
