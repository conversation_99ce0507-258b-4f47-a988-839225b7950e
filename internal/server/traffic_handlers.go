package server

import (
	"log"
	"sync/atomic"
	"time"
)

// ServerTrafficHandler 服务器级流量统计处理器（直接引用Server统计字段）
type ServerTrafficHandler struct {
	server *Server // 直接引用Server实例
}

// NewServerTrafficHandler 创建服务器级流量统计处理器
func NewServerTrafficHandler(server *Server) *ServerTrafficHandler {
	return &ServerTrafficHandler{
		server: server,
	}
}

func (h *ServerTrafficHandler) HandleTraffic(event *TrafficEvent) {
	// 直接更新Server的统计字段
	atomic.AddInt64(&h.server.totalBytesUploaded, event.Uploaded)
	atomic.AddInt64(&h.server.totalBytesDownloaded, event.Downloaded)
}

func (h *ServerTrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_uploaded":   atomic.LoadInt64(&h.server.totalBytesUploaded),
		"total_downloaded": atomic.LoadInt64(&h.server.totalBytesDownloaded),
		"uptime_seconds":   time.Since(h.server.startTime).Seconds(),
		"start_time":       h.server.startTime.Unix(),
	}
}

func (h *ServerTrafficHandler) GetName() string {
	return "server_quic"
}

func (h *ServerTrafficHandler) Close() {
	// 服务器级处理器无需特殊清理
}

// HTTP2TrafficHandler HTTP2服务器级流量统计处理器
type HTTP2TrafficHandler struct {
	server *HTTP2Server // 直接引用HTTP2Server实例
}

// NewHTTP2TrafficHandler 创建HTTP2服务器级流量统计处理器
func NewHTTP2TrafficHandler(server *HTTP2Server) *HTTP2TrafficHandler {
	return &HTTP2TrafficHandler{
		server: server,
	}
}

func (h *HTTP2TrafficHandler) HandleTraffic(event *TrafficEvent) {
	// 直接更新HTTP2Server的统计字段
	atomic.AddInt64(&h.server.totalBytesUploaded, event.Uploaded)
	atomic.AddInt64(&h.server.totalBytesDownloaded, event.Downloaded)
}

func (h *HTTP2TrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_uploaded":   atomic.LoadInt64(&h.server.totalBytesUploaded),
		"total_downloaded": atomic.LoadInt64(&h.server.totalBytesDownloaded),
		"uptime_seconds":   time.Since(h.server.startTime).Seconds(),
		"start_time":       h.server.startTime.Unix(),
	}
}

func (h *HTTP2TrafficHandler) GetName() string {
	return "server_http2"
}

func (h *HTTP2TrafficHandler) Close() {
	// HTTP2服务器级处理器无需特殊清理
}

// UserTrafficHandler 用户级流量统计处理器（集成用户管理功能）
type UserTrafficHandler struct {
	connectionManager *ConnectionManager // 连接管理器
	authManager       *AuthManager       // 用户认证管理器
}

// NewUserTrafficHandler 创建用户级流量统计处理器
func NewUserTrafficHandler(connectionManager *ConnectionManager, authManager *AuthManager) *UserTrafficHandler {
	return &UserTrafficHandler{
		connectionManager: connectionManager,
		authManager:       authManager,
	}
}

func (h *UserTrafficHandler) HandleTraffic(event *TrafficEvent) {
	// 统一通过token查找AuthState（支持QUIC和HTTP2）
	if event.AuthToken != "" && h.authManager != nil {
		// 使用AuthManager的公开方法获取AuthState
		if authState := h.authManager.GetAuthState(event.AuthToken); authState != nil {
			// 原子更新流量统计
			atomic.AddInt64(&authState.BytesUp, event.Uploaded)
			atomic.AddInt64(&authState.BytesDown, event.Downloaded)
			authState.LastActivity = time.Now()

			log.Printf("📊 用户流量更新: UserID=%s, Token=%s, ↑+%d ↓+%d, 总计: ↑%d ↓%d",
				authState.UserID, maskToken(authState.Token),
				event.Uploaded, event.Downloaded,
				atomic.LoadInt64(&authState.BytesUp), atomic.LoadInt64(&authState.BytesDown))
		} else {
			log.Printf("⚠️  未找到对应的AuthState: Token=%s, TrafficType=%s",
				maskToken(event.AuthToken), event.TrafficType)
		}
	} else {
		log.Printf("⚠️  流量事件缺少AuthToken或AuthManager未初始化: ConnectionID=%s, TrafficType=%s",
			event.ConnectionID, event.TrafficType)
	}
}

func (h *UserTrafficHandler) GetStats() map[string]interface{} {
	users := make([]map[string]interface{}, 0)
	totalUsers := 0
	totalBytesUp := int64(0)
	totalBytesDown := int64(0)

	// 通过AuthManager获取所有在线用户统计
	if h.authManager != nil {
		h.authManager.authCache.Range(func(key, value interface{}) bool {
			authState := value.(*AuthState)

			// 检查AuthState是否有效
			if authState != nil && !authState.IsDestroyed() {
				userStats := map[string]interface{}{
					"user_id":       authState.UserID,
					"client_id":     authState.ClientID,
					"token":         maskToken(authState.Token),
					"ip":            authState.Ip,
					"auth_time":     authState.AuthTime.Unix(),
					"expires_at":    authState.ExpiresAt.Unix(),
					"last_activity": authState.LastActivity.Unix(),
					"stream_count":  authState.StreamCount,
					"bytes_up":      atomic.LoadInt64(&authState.BytesUp),
					"bytes_down":    atomic.LoadInt64(&authState.BytesDown),
					"is_auth":       authState.IsAuth,
				}

				// 如果有QUIC连接，添加连接信息
				if quicConn := authState.GetQUICConnectionHandler(); quicConn != nil {
					userStats["connection_id"] = quicConn.GetConnID()
					userStats["connection_type"] = "QUIC"
				} else {
					userStats["connection_type"] = "HTTP2"
				}

				users = append(users, userStats)
				totalUsers++
				totalBytesUp += atomic.LoadInt64(&authState.BytesUp)
				totalBytesDown += atomic.LoadInt64(&authState.BytesDown)
			}
			return true
		})
	}

	return map[string]interface{}{
		"total_users":      totalUsers,
		"total_bytes_up":   totalBytesUp,
		"total_bytes_down": totalBytesDown,
		"users":            users,
	}
}

func (h *UserTrafficHandler) GetName() string {
	return "user"
}

func (h *UserTrafficHandler) Close() {
	// 用户级处理器无需特殊清理，AuthManager 自己管理生命周期
}

// GetUserByToken 通过token获取用户信息
func (h *UserTrafficHandler) GetUserByToken(token string) *AuthState {
	if h.authManager != nil {
		return h.authManager.GetAuthState(token)
	}
	return nil
}

// GetOnlineUserCount 获取在线用户数量
func (h *UserTrafficHandler) GetOnlineUserCount() int {
	if h.authManager != nil {
		return h.authManager.GetOnlineTotalUsers()
	}
	return 0
}

// GetUserStats 获取指定用户的统计信息
func (h *UserTrafficHandler) GetUserStats(token string) map[string]interface{} {
	if authState := h.GetUserByToken(token); authState != nil {
		stats := map[string]interface{}{
			"user_id":       authState.UserID,
			"client_id":     authState.ClientID,
			"ip":            authState.Ip,
			"auth_time":     authState.AuthTime.Unix(),
			"last_activity": authState.LastActivity.Unix(),
			"stream_count":  authState.StreamCount,
			"bytes_up":      atomic.LoadInt64(&authState.BytesUp),
			"bytes_down":    atomic.LoadInt64(&authState.BytesDown),
			"is_auth":       authState.IsAuth,
		}

		// 添加连接信息
		if quicConn := authState.GetQUICConnectionHandler(); quicConn != nil {
			stats["connection_id"] = quicConn.GetConnID()
			stats["connection_type"] = "QUIC"
		} else {
			stats["connection_type"] = "HTTP2"
		}

		return stats
	}
	return nil
}

type TotalTrafficHandler struct {
	server *MultiProtocolServer
}

func NewTotalTrafficHandler(server *MultiProtocolServer) *TotalTrafficHandler {
	return &TotalTrafficHandler{
		server: server,
	}
}

func (h *TotalTrafficHandler) HandleTraffic(event *TrafficEvent) {
	atomic.AddInt64(&h.server.bytesUp, event.Uploaded)
	atomic.AddInt64(&h.server.bytesDown, event.Downloaded)
}

func (h *TotalTrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_uploaded":   atomic.LoadInt64(&h.server.bytesUp),
		"total_downloaded": atomic.LoadInt64(&h.server.bytesDown),
	}
}

func (h *TotalTrafficHandler) GetName() string {
	return "total"
}

func (h *TotalTrafficHandler) Close() {
	// 总流量处理器无需特殊清理
}
