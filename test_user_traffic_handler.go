package main

import (
	"log"
	"sync/atomic"
	"time"

	"socks5-quic-server/internal/server"
)

// maskToken 掩码Token显示
func maskToken(token string) string {
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
}

func main() {
	log.Printf("🧪 测试UserTrafficHandler用户管理功能")

	// 1. 创建必要的组件
	authManager := server.NewAuthManager(nil)
	connectionManager := server.NewConnectionManager(100, 5*time.Minute)
	trafficManager := server.NewTrafficManager(100)

	// 2. 创建UserTrafficHandler（新的API，传入AuthManager）
	userHandler := server.NewUserTrafficHandler(connectionManager, authManager)
	trafficManager.RegisterHandler(userHandler)

	// 3. 创建测试用户
	testToken := "test-token-user-mgmt"
	testAuthState := &server.AuthState{
		Ip:           "*************",
		UserID:       "user-12345",
		Token:        testToken,
		ClientID:     "client-abc",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
		StreamCount:  0,
		BytesUp:      0,
		BytesDown:    0,
	}

	// 启动上报
	testAuthState.StartReporting(10 * time.Second)

	log.Printf("✅ 创建测试用户: UserID=%s, Token=%s", testAuthState.UserID, maskToken(testAuthState.Token))

	// 4. 创建一个测试专用的流量处理器来模拟用户管理功能
	testUserHandler := &TestUserTrafficHandler{
		authState: testAuthState,
		token:     testToken,
	}
	trafficManager.RegisterHandler(testUserHandler)

	// 4. 测试UserTrafficHandler的基本功能
	log.Printf("📋 测试1：UserTrafficHandler基本功能")

	// 测试在线用户数量（由于我们没有真正的认证，这里会返回0）
	onlineCount := userHandler.GetOnlineUserCount()
	log.Printf("✅ 当前在线用户数量: %d", onlineCount)

	// 测试获取用户信息（由于没有真正的认证缓存，这里会返回nil）
	retrievedUser := userHandler.GetUserByToken(testToken)
	if retrievedUser == nil {
		log.Printf("✅ 预期行为：未认证的token返回nil")
	} else {
		log.Printf("❌ 意外行为：未认证的token返回了用户信息")
	}

	// 5. 测试流量事件处理（通过TestUserTrafficHandler）
	log.Printf("📋 测试2：流量事件处理")

	// 发送流量事件（这会被TestUserTrafficHandler处理）
	trafficManager.UpdateTraffic(
		server.TrafficTypeQUIC,
		"stream-test-1",
		"conn-test-1",
		testToken,
		testAuthState.UserID,
		"example.com:443",
		1024, // 上传
		2048, // 下载
	)

	// 等待事件处理
	time.Sleep(200 * time.Millisecond)

	// 验证流量统计（通过TestUserTrafficHandler更新）
	currentUp := atomic.LoadInt64(&testAuthState.BytesUp)
	currentDown := atomic.LoadInt64(&testAuthState.BytesDown)

	if currentUp == 1024 && currentDown == 2048 {
		log.Printf("✅ 流量统计更新正确: ↑%d ↓%d", currentUp, currentDown)
	} else {
		log.Printf("❌ 流量统计更新错误: 期望 ↑1024 ↓2048, 实际 ↑%d ↓%d", currentUp, currentDown)
	}

	// 6. 测试UserTrafficHandler的统计功能
	log.Printf("📋 测试3：UserTrafficHandler统计功能")

	// 测试获取用户统计信息（由于没有真正的认证缓存，这里会返回nil）
	userStats := userHandler.GetUserStats(testToken)
	if userStats == nil {
		log.Printf("✅ 预期行为：未认证用户的统计信息返回nil")
	} else {
		log.Printf("❌ 意外行为：未认证用户返回了统计信息")
	}

	// 测试获取所有用户统计（由于没有真正的认证缓存，用户数为0）
	allStats := userHandler.GetStats()
	if allStats != nil {
		totalUsers := allStats["total_users"].(int)
		log.Printf("✅ 所有用户统计: 总用户数=%d", totalUsers)
		if totalUsers == 0 {
			log.Printf("✅ 预期行为：没有认证用户时总数为0")
		}
	} else {
		log.Printf("❌ 无法获取所有用户统计")
	}

	// 7. 再发送一些流量事件测试累积
	log.Printf("📋 测试4：累积流量统计")
	for i := 0; i < 3; i++ {
		trafficManager.UpdateTraffic(
			server.TrafficTypeHTTP2,
			"stream-http2-"+string(rune(i)),
			"",
			testToken,
			testAuthState.UserID,
			"api.example.com:443",
			512,  // 上传
			1024, // 下载
		)
		time.Sleep(50 * time.Millisecond)
	}

	time.Sleep(200 * time.Millisecond)

	// 验证累积统计
	finalUp := atomic.LoadInt64(&testAuthState.BytesUp)
	finalDown := atomic.LoadInt64(&testAuthState.BytesDown)
	expectedUp := int64(1024 + 3*512)  // 1024 + 1536 = 2560
	expectedDown := int64(2048 + 3*1024) // 2048 + 3072 = 5120

	if finalUp == expectedUp && finalDown == expectedDown {
		log.Printf("✅ 累积流量统计正确: ↑%d ↓%d", finalUp, finalDown)
	} else {
		log.Printf("❌ 累积流量统计错误: 期望 ↑%d ↓%d, 实际 ↑%d ↓%d",
			expectedUp, expectedDown, finalUp, finalDown)
	}

	// 8. 清理资源
	trafficManager.Close()
	connectionManager.Stop()
	testAuthState.Destroy()

	log.Printf("🎉 UserTrafficHandler用户管理功能测试完成")
}

// TestUserTrafficHandler 测试用流量处理器
type TestUserTrafficHandler struct {
	authState *server.AuthState
	token     string
}

func (h *TestUserTrafficHandler) HandleTraffic(event *server.TrafficEvent) {
	// 只处理匹配token的事件
	if event.AuthToken == h.token {
		// 直接更新AuthState统计（模拟UserTrafficHandler的逻辑）
		atomic.AddInt64(&h.authState.BytesUp, event.Uploaded)
		atomic.AddInt64(&h.authState.BytesDown, event.Downloaded)
		h.authState.LastActivity = time.Now()

		log.Printf("🔧 TestUserHandler处理事件: 上传+%d, 下载+%d, Token=%s",
			event.Uploaded, event.Downloaded, maskToken(event.AuthToken))
	}
}

func (h *TestUserTrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"bytes_up":      atomic.LoadInt64(&h.authState.BytesUp),
		"bytes_down":    atomic.LoadInt64(&h.authState.BytesDown),
		"last_activity": h.authState.LastActivity.Unix(),
	}
}

func (h *TestUserTrafficHandler) GetName() string {
	return "test_user_handler"
}

func (h *TestUserTrafficHandler) Close() {
	// 测试处理器无需特殊清理
}
