package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"socks5-quic-server/internal"
	"socks5-quic-server/internal/client"
)

func init() {
	const (
		socks5Port = 1080
		serverAddr = "localhost:8443" // 代理服务器地址
	)

	log.Println("启动SOCKS5代理服务器示例...")

	// 创建SOCKS5服务器
	socks5Server := internal.NewSocks5Server(
		socks5Port,
		serverAddr,
	)

	// 启动服务器
	if err := socks5Server.Start(); err != nil {
		log.Fatalf("启动SOCKS5服务器失败: %v", err)
	}

}
func main() {
	fmt.Println("=== 节点列表管理功能演示 ===")

	// 获取全局客户端管理器
	manager := client.GetGlobalClientManager()

	// 演示1: 从URL添加节点
	fmt.Println("\n1. 从URL添加节点")

	// 添加多个QUIC节点
	token := "eyJ0eXAiOiJqd3QifQ.eyJzdWIiOiIxIiwiaXNzIjoiaHR0cDpcL1wvOiIsImV4cCI6MTc1MTQ0NzM5OSwiaWF0IjoxNzUwODQyNTk5LCJuYmYiOjE3NTA4NDI1OTksInVpZCI6MSwicyI6IjZTY3VtaSIsImp0aSI6IjlhMDk1MTU3MGJhNGZlNTMzNjEzM2JhOGMxYzQ3NDg4In0.JDJ5JDEwJHBlLnVhTjY3MGFaUEJEMFdodjJvNU9BemtYMTR0dWxuYzByYXk5c1NnNS94U0FiUkU1emRT"
	//token := "eyJ0eXAiOiJqd3QifQ.eyJzdWIiOiIxIiwiaXNzIjoiaHR0cDpcL1wvOiIsImV4cCI6MTc1MTQ0OTgwOSwiaWF0IjoxNzUwODQ1MDA5LCJuYmYiOjE3NTA4NDUwMDksInVpZCI6MTcsInMiOiJJRm8yMFkiLCJqdGkiOiJkMDY0MjY5YTU0NWRiZjg1MjdmMGIzM2QzZjlhMzU4YyJ9.JDJ5JDEwJHRSZDF0U2QuVnpTM2gxZ0RFTzVRZk9EbElwMUF1ZVZZYVhiWTNaZzJaUDV5eGtkR1pSekMu"
	quicNodes := []string{
		"quic://**************:8443?skipTLSVerify=1&token=" + token,
	}
	jsonStr, _ := json.Marshal(quicNodes)
	manager.SetNodesFromStr(string(jsonStr))
	manager.Connect()
	// 演示2: 获取节点信息
	fmt.Println("\n2. 获取节点信息")

	// 获取所有节点
	allNodes := manager.GetNodes()
	fmt.Printf("总共有 %d 个节点\n", len(allNodes))

	fmt.Println("\n=== 演示完成 ===")
	for {
		time.Sleep(time.Second * 3)
	}
}
